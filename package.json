{"name": "b1", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.21.1", "express-fileupload": "^1.5.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.0", "node-schedule": "^2.1.1", "nodemailer": "^6.10.0", "nodemon": "^3.1.7", "otp-generator": "^4.0.1", "razorpay": "^2.9.6", "router": "^1.3.8"}}