const CourseProgress = require("../models/CourseProgress");
const SubSection = require("../models/SubSection");

exports.updateCourseProgress = async (req, res) => {
  try {
    const { courseId, subsectionId } = req.body;
    const userId = req.user.id;

    // Validation
    if (!courseId || !subsectionId) {
      return res.status(400).json({
        success: false,
        message: "All fields are required",
      });
    }

    // Check if subsection is valid
    const subsection = await SubSection.findById(subsectionId);
    if (!subsection) {
      return res.status(404).json({
        success: false,
        message: "Invalid subsection",
      });
    }

    // Find the course progress document for this course and user
    let courseProgress = await CourseProgress.findOne({
      courseID: courseId,
      userId: userId,
    });

    // If no course progress document exists, create a new one
    if (!courseProgress) {
      courseProgress = await CourseProgress.create({
        courseID: courseId,
        userId: userId,
        completedVideos: [subsectionId],
      });
    } else {
      // If the subsection is not already completed, add it to the completedVideos array
      if (!courseProgress.completedVideos.includes(subsectionId)) {
        courseProgress.completedVideos.push(subsectionId);
      }
      await courseProgress.save();
    }

    return res.status(200).json({
      success: true,
      message: "Course progress updated successfully",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Error updating course progress",
      error: error.message,
    });
  }
};
