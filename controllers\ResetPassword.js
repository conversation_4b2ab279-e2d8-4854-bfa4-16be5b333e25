const User = require('../models/User');
const mailSender = require('../utils/mailSender');
const bcrypt = require('bcrypt');
const crypto = require('crypto');

// resetPasswordToken: User ke email par ek password reset link bhejta hai jo token aur expiry time ke saath hota hai.
// resetPassword: User ka password reset karta hai agar token valid aur expire nahi hua ho. Password securely hash karke database mein store kiya jata hai.


// reset password token
exports.resetPasswordToken = async(req,res)=>{
    try {
        // fetch email
        const email = req.body.email;
        // validate
        if(!email){
            return res.status(400).json({
                success:false,
                message:'Email is required'
            })
        }
        const user = await User.findOne({email});
        if(!user){
            return res.status(400).json({
                success:false,
                message:'Email is not registered.'
            })
        }
        //generate token
        const token = crypto.randomBytes(20).toString('hex');
        // update user by adding token and expiration time
        const updatedDetails = await User.findOneAndUpdate({email},
            {
                token,
                resetPasswordExpires:Date.now()+ 5*60*1000
            },
            {new:true}// provide updated details
        )
        // create url
        const url = `http://localhost:3000/update-password/${token}`;
        // send mail
        await mailSender(email,"Reset Password Link",`Password reset link : ${url}`);
        // return response
        return res.status(200).json({
            success:true,
            message:'Email sent successfully, please check your email'
        })
    } catch (error) {
        console.log(error.message);
        return res.status(500).json({
            success:false,
            message:'Something went wrong while sending email'
        })
    }
}
// reset password
exports.resetPassword = async(req,res)=>{
    try {
        //fetch data
        const {password,confirmPassword,token} = req.body;
        // validate password
        if(password !== confirmPassword){
            return res.status(400).json({
                success:false,
                message:'Password and Confirm Password does not match'
            })
        }
        // get updated user details using token
        const userDetails = await User.findOne({token});
        if(!userDetails){
            return res.status(400).json({
                success:false,
                message:'Token is invalid'
            })
        }
        // check expiry of token
        if(userDetails.resetPasswordExpires < Date.now()){
            return res.status(400).json({
                success:false,
                message:'Token is expired'
            })
        }
        // hash password
        const hashedPassword = await bcrypt.hash(password,10);
        // store in db
        await User.findOneAndUpdate({token},
            {
                password:hashedPassword,
            }, {new:true}
        )
        return res.status(200).json({
            success:true,
            message:'Password reset successfully'
        })
        
    } catch (error) {
        console.log(error.message);
        return res.status(500).json({
            success:false,
            message:'Something went wrong while resetting password'
        })
    }
} 