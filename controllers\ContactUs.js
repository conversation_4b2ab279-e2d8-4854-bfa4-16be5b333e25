const mailSender = require("../utils/mailSender");
const { contactUsEmail } = require("../mail/templates/contactFormRes");

exports.contactUsController = async (req, res) => {
  try {
    const { email, firstname, lastname, message, phoneNo, countrycode } = req.body;

    // Validation
    if (!email || !firstname || !lastname || !message) {
      return res.status(400).json({
        success: false,
        message: "All fields are required",
      });
    }

    // Send email to the user
    try {
      const emailRes = await mailSender(
        email,
        "Your Data send successfully",
        contactUsEmail(email, firstname, lastname, message, phoneNo, countrycode)
      );
      console.log("Email Res ", emailRes);
    } catch (error) {
      console.log("Error", error);
      console.log("Error message :", error.message);
    }

    return res.json({
      success: true,
      message: "Email send successfully",
    });
  } catch (error) {
    console.log("Error", error);
    console.log("Error message :", error.message);
    return res.status(500).json({
      success: false,
      message: "Something went wrong...",
    });
  }
};
