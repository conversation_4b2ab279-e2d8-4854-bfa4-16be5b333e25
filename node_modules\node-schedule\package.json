{"name": "node-schedule", "version": "2.1.1", "description": "A cron-like and not-cron-like job scheduler for Node.", "keywords": ["schedule", "task", "job", "cron", "recurrent", "in-memory"], "license": "MIT", "main": "index.js", "scripts": {"test": "tape test/*.js", "test:browser": "airtap test/cancel-long-running-jobs.js test/convenience-method-test.js test/date-convenience-methods-test.js test/range-test.js test/recurrence-rule-test.js test/schedule-cron-jobs.js test/start-end-test.js", "coveralls": "nyc report --reporter=lcov", "test:coverage": "nyc tape test/*.js", "lint": "eslint lib test", "lint:fix": "eslint --fix lib test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mattpatenaude.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/node-schedule/node-schedule.git"}, "dependencies": {"cron-parser": "^4.2.0", "long-timeout": "0.1.1", "sorted-array-functions": "^1.3.0"}, "devDependencies": {"airtap": "^4.0.4", "eslint": "^8.3.0", "nyc": "^15.1.0", "sinon": "^9.2.4", "tape": "^5.3.2"}, "engines": {"node": ">=6"}, "files": ["CHANGELOG.md", "README.md", "UPGRADING.md", "LICENSE", "lib/*"]}